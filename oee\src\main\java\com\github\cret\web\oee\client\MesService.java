package com.github.cret.web.oee.client;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.github.cret.web.oee.document.mes.LabelLatestStatus;
import com.github.cret.web.oee.domain.mes.StandardCapacity;

/**
 * MES服务接口
 */
@Service
public class MesService {

	private static final Logger log = LoggerFactory.getLogger(MesService.class);

	private final RestTemplate restTemplate;

	@Value("${mes.label-work-process-url}")
	private String labelWorkProcessUrl;

	@Value("${mes.sample-url}")
	private String sampleUrl;

	public MesService(RestTemplate restTemplate) {
		this.restTemplate = restTemplate;
	}

	public StandardCapacity fetchStandardCapacity(String baseUrl, String prodId, String plId, String bomSide) {
		// 构建请求的 URL
		String url = baseUrl + "/oee/standardCapacity?prodId=" + prodId + "&plId=" + plId + "&bomSide=" + bomSide;
		// 发送 GET 请求并接收响应
		return restTemplate.getForObject(url, StandardCapacity.class);
	}

	/**
	 * 调用 /latest-status 接口获取最新的标签状态
	 * @param lbIds 需要查询的 ID 列表
	 * @return 状态列表
	 */
	public List<LabelLatestStatus> fetchLatestStatus(List<String> lbIds) {
		// 1. 设置请求头
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);

		// 2. 创建请求体
		// HttpEntity 包装了请求头和请求体
		HttpEntity<List<String>> requestEntity = new HttpEntity<>(lbIds, headers);

		// 3. 发起 POST 请求
		// 使用 ParameterizedTypeReference 来处理泛型响应 List<LabelLatestStatus>
		ParameterizedTypeReference<List<LabelLatestStatus>> responseType = new ParameterizedTypeReference<List<LabelLatestStatus>>() {
		};

		// 4. 使用 exchange 方法，它更通用，可以获取完整的响应信息（包括状态码、头等）
		ResponseEntity<List<LabelLatestStatus>> responseEntity = restTemplate
			.exchange(labelWorkProcessUrl + "/latest-status", HttpMethod.POST, requestEntity, responseType);

		// 检查响应状态码是否为 2xx
		if (responseEntity.getStatusCode().is2xxSuccessful()) {
			return responseEntity.getBody();
		}
		else {
			log.error("请求 /latest-status 失败，状态码: {}", responseEntity.getStatusCode());
			return Collections.emptyList();
		}
	}

	/**
	 * 调用 /latest-status 接口获取最新的标签状态
	 * @param lbIds 需要查询的 ID 列表
	 * @return 状态列表
	 */
	public List<String> fetchSamples(List<String> lbIds) {
		// 1. 设置请求头
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);

		// 2. 创建请求体
		// HttpEntity 包装了请求头和请求体
		HttpEntity<List<String>> requestEntity = new HttpEntity<>(lbIds, headers);

		// 3. 发起 POST 请求
		// 使用 ParameterizedTypeReference 来处理泛型响应 List<LabelLatestStatus>
		ParameterizedTypeReference<List<String>> responseType = new ParameterizedTypeReference<List<String>>() {
		};

		// 4. 使用 exchange 方法，它更通用，可以获取完整的响应信息（包括状态码、头等）
		ResponseEntity<List<String>> responseEntity = restTemplate.exchange(sampleUrl + "/ids-by-lbids",
				HttpMethod.POST, requestEntity, responseType);

		// 检查响应状态码是否为 2xx
		if (responseEntity.getStatusCode().is2xxSuccessful()) {
			return responseEntity.getBody();
		}
		else {
			log.error("请求 /latest-status 失败，状态码: {}", responseEntity.getStatusCode());
			return Collections.emptyList();
		}
	}

}
